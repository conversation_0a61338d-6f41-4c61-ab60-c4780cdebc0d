import { useEffect, useState } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/serverComm';
import { BusinessProfile } from '@/components/business/BusinessProfile';
import { SEOHead } from '@/components/seo/SEOHead';
import { ArrowLeft, AlertCircle } from 'lucide-react';

interface BusinessData {
  id: number;
  name: string;
  slug: string;
  description: string;
  short_description: string;
  address: string;
  latitude: string;
  longitude: string;
  phone: string;
  email: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  created_at: string;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
    description: string;
  };
  hours: Array<{
    id: number;
    day_of_week: string;
    open_time: string;
    close_time: string;
    is_closed: boolean;
  }>;
  images: Array<{
    id: number;
    image_url: string;
    alt_text: string;
    is_primary: boolean;
    display_order: number;
  }>;
  reviews: Array<{
    id: number;
    rating: number;
    comment: string;
    author_name: string;
    created_at: string;
    is_verified: boolean;
  }>;
}

export function BusinessProfilePage() {
  const { slug } = useParams<{ slug: string }>();
  const [business, setBusiness] = useState<BusinessData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchBusiness() {
      if (!slug) return;
      
      try {
        setLoading(true);
        const response = await api.getBusiness(slug);
        setBusiness(response);
        setError(null);
      } catch (err: any) {
        if (err.status === 404) {
          setError('Business not found');
        } else {
          setError('Failed to load business information');
        }
        console.error('Error fetching business:', err);
      } finally {
        setLoading(false);
      }
    }

    fetchBusiness();
  }, [slug]);

  // Update page title when business loads
  useEffect(() => {
    if (business) {
      document.title = `${business.name} - Business Directory`;
    }
    return () => {
      document.title = 'Business Directory';
    };
  }, [business]);

  if (loading) {
    return (
      <div className="min-h-screen">
        {/* Loading Header */}
        <div className="h-64 bg-muted animate-pulse"></div>
        
        <div className="container mx-auto p-6">
          <div className="space-y-6">
            <div className="flex items-center gap-4">
              <div className="w-8 h-8 bg-muted rounded animate-pulse"></div>
              <div className="h-6 bg-muted rounded w-32 animate-pulse"></div>
            </div>
            
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-6">
                <div className="h-8 bg-muted rounded w-64 animate-pulse"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                  <div className="h-4 bg-muted rounded w-3/4 animate-pulse"></div>
                </div>
              </div>
              
              <div className="space-y-4">
                <div className="h-6 bg-muted rounded w-32 animate-pulse"></div>
                <div className="space-y-2">
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                  <div className="h-4 bg-muted rounded animate-pulse"></div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (error || !business) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <AlertCircle className="w-16 h-16 mx-auto text-muted-foreground" />
          <h1 className="text-2xl font-bold">Business Not Found</h1>
          <p className="text-muted-foreground">
            {error || 'The business you\'re looking for could not be found.'}
          </p>
          <div className="flex flex-col sm:flex-row gap-3 justify-center">
            <Button variant="outline" asChild>
              <Link to="/businesses">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Browse Businesses
              </Link>
            </Button>
            <Button asChild>
              <Link to="/categories">View Categories</Link>
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      {/* SEO Head */}
      <SEOHead
        title={`${business.name} - ${business.category.name}`}
        description={business.description || business.short_description}
        keywords={[
          business.name,
          business.category.name,
          'business',
          'local',
          business.address.split(',').pop()?.trim() || ''
        ].filter(Boolean)}
        image={business.hero_image_url}
        type="business.business"
        businessData={{
          name: business.name,
          address: business.address,
          phone: business.phone,
          website: business.website,
          category: business.category.name,
          rating: business.total_reviews > 0 ? parseFloat(business.average_rating) : undefined,
          reviewCount: business.total_reviews,
          hours: business.hours
        }}
      />

      {/* Back Navigation */}
      <div className="container mx-auto p-6 pb-0">
        <Button variant="ghost" size="sm" asChild>
          <Link to="/businesses">
            <ArrowLeft className="w-4 h-4 mr-2" />
            Back to Businesses
          </Link>
        </Button>
      </div>

      {/* Business Profile */}
      <BusinessProfile business={business} />
    </div>
  );
}
