import { useEffect, useState } from 'react';
import { api } from '@/lib/serverComm';

interface Business {
  id: number;
  name: string;
  category: {
    name: string;
  };
}

export function MarqueeBanner() {
  const [businesses, setBusinesses] = useState<Business[]>([]);

  useEffect(() => {
    async function fetchBusinesses() {
      try {
        const response = await api.getBusinesses({ limit: 20 });
        // Ensure we have a valid array of businesses
        if (response && response.businesses && Array.isArray(response.businesses)) {
          setBusinesses(response.businesses);
        } else {
          // Use fallback data if response structure is unexpected
          setBusinesses([
            { id: 1, name: "Joe's Coffee Shop", category: { name: "Restaurant" } },
            { id: 2, name: "Tech Solutions Inc", category: { name: "Technology" } },
            { id: 3, name: "Green Thumb Landscaping", category: { name: "Home & Garden" } },
            { id: 4, name: "Bella's Boutique", category: { name: "Fashion" } },
            { id: 5, name: "Quick Fix Auto", category: { name: "Automotive" } },
          ]);
        }
      } catch (err) {
        console.error('Error fetching businesses for marquee:', err);
        // Fallback to static data if API fails
        setBusinesses([
          { id: 1, name: "Joe's Coffee Shop", category: { name: "Restaurant" } },
          { id: 2, name: "Tech Solutions Inc", category: { name: "Technology" } },
          { id: 3, name: "Green Thumb Landscaping", category: { name: "Home & Garden" } },
          { id: 4, name: "Bella's Boutique", category: { name: "Fashion" } },
          { id: 5, name: "Quick Fix Auto", category: { name: "Automotive" } },
        ]);
      }
    }

    fetchBusinesses();
  }, []);

  // Create a continuous list by duplicating the businesses, ensuring we have an array
  const marqueeItems = Array.isArray(businesses) && businesses.length > 0 
    ? [...businesses, ...businesses] 
    : [];

  // Don't render anything if there are no items
  if (marqueeItems.length === 0) {
    return null;
  }

  return (
    <div className="bg-primary text-primary-foreground py-3 overflow-hidden">
      <div className="relative">
        <div className="flex animate-marquee whitespace-nowrap">
          {marqueeItems.map((business, index) => (
            <span
              key={`${business.id}-${index}`}
              className="mx-8 text-sm font-medium"
            >
              {business.name} • {business.category?.name || 'Business'}
            </span>
          ))}
        </div>
      </div>
    </div>
  );
}

// Add the marquee animation to your global CSS (index.css)
// @keyframes marquee {
//   0% {
//     transform: translateX(0%);
//   }
//   100% {
//     transform: translateX(-50%);
//   }
// }
// 
// .animate-marquee {
//   animation: marquee 30s linear infinite;
// }
