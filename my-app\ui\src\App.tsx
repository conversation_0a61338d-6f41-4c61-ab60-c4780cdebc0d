import { AuthProvider, useAuth } from '@/lib/auth-context';
import { ThemeProvider } from "@/components/theme-provider";
import { LoginForm } from '@/components/login-form';
import { Navbar } from '@/components/navbar';
import { AppSidebar } from '@/components/appSidebar';
import { Settings } from '@/pages/Settings';
import { HomePage } from '@/pages/HomePage';
import { CategoriesPage } from '@/pages/CategoriesPage';
import { CategoryDetailPage } from '@/pages/CategoryDetailPage';
import { BusinessProfilePage } from '@/pages/BusinessProfilePage';
import { SearchPage } from '@/pages/SearchPage';
import { SearchResultsPage } from '@/pages/SearchResultsPage';
import { BusinessListPage } from '@/pages/BusinessListPage';
import { ApplicationPage } from '@/pages/ApplicationPage';
import { PublicHeader } from '@/components/navigation/PublicHeader';
import { AdminRoute } from '@/components/admin/AdminRoute';
import { AdminDashboard } from '@/pages/admin/AdminDashboard';
import { AdminBusinessesPage } from '@/pages/admin/AdminBusinessesPage';
import { AdminApplicationsPage } from '@/pages/admin/AdminApplicationsPage';
import { AdminCategoriesPage } from '@/pages/admin/AdminCategoriesPage';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import {
  SidebarProvider,
  SidebarInset,
} from "@/components/ui/sidebar";

function AppContent() {
  const { user, loading } = useAuth();

  if (loading) {
    return <div className="flex items-center justify-center min-h-screen"></div>;
  }

  return (
    <div className="flex flex-col w-full min-h-screen bg-background">
      <Routes>
        {/* Public Business Directory Routes */}
        <Route path="/" element={
          <>
            <PublicHeader />
            <HomePage />
          </>
        } />
        <Route path="/search" element={
          <>
            <PublicHeader />
            <SearchResultsPage />
          </>
        } />
        <Route path="/businesses" element={
          <>
            <PublicHeader />
            <BusinessListPage />
          </>
        } />
        <Route path="/apply" element={
          <>
            <PublicHeader />
            <ApplicationPage />
          </>
        } />
        <Route path="/categories" element={
          <>
            <PublicHeader />
            <CategoriesPage />
          </>
        } />
        <Route path="/categories/:slug" element={
          <>
            <PublicHeader />
            <CategoryDetailPage />
          </>
        } />
        <Route path="/business/:slug" element={
          <>
            <PublicHeader />
            <BusinessProfilePage />
          </>
        } />

        {/* Admin/Authenticated Routes */}
        <Route path="/admin/*" element={
          <SidebarProvider>
            <Navbar />
            {!user ? (
              <main className="flex flex-col items-center justify-center flex-1 p-4">
                <LoginForm />
              </main>
            ) : (
              <div className="flex flex-1">
                <AppSidebar />
                <SidebarInset className="flex-1">
                  <main className="flex-1">
                    <Routes>
                      <Route path="/" element={<AdminRoute><AdminDashboard /></AdminRoute>} />
                      <Route path="/businesses" element={<AdminRoute><AdminBusinessesPage /></AdminRoute>} />
                      <Route path="/categories" element={<AdminRoute><AdminCategoriesPage /></AdminRoute>} />
                      <Route path="/reviews" element={<AdminRoute><div className="p-6">Admin Reviews Page - Coming Soon</div></AdminRoute>} />
                      <Route path="/users" element={<AdminRoute><div className="p-6">Admin Users Page - Coming Soon</div></AdminRoute>} />
                      <Route path="/applications" element={<AdminRoute><AdminApplicationsPage /></AdminRoute>} />
                      <Route path="/settings" element={<AdminRoute><Settings /></AdminRoute>} />
                    </Routes>
                  </main>
                </SidebarInset>
              </div>
            )}
          </SidebarProvider>
        } />
      </Routes>
    </div>
  );
}

function App() {
  return (
    <AuthProvider>
      <ThemeProvider 
        attribute="class" 
        defaultTheme="system" 
        enableSystem
        disableTransitionOnChange
        storageKey="volo-app-theme"
      >
        <Router>
          <AppContent />
        </Router>
      </ThemeProvider>
    </AuthProvider>
  );
}

export default App;
