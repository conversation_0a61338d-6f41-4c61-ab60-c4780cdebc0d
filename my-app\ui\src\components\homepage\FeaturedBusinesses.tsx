import { useEffect, useState } from 'react';
import { <PERSON> } from 'react-router-dom';
import { <PERSON>, CardContent, CardHeader } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { api } from '@/lib/serverComm';
import { Star, MapPin, Phone, Globe } from 'lucide-react';

interface Business {
  id: number;
  name: string;
  slug: string;
  short_description: string;
  address: string;
  phone: string;
  website: string;
  logo_url: string;
  hero_image_url: string;
  is_featured: boolean;
  average_rating: string;
  total_reviews: number;
  category: {
    id: number;
    name: string;
    slug: string;
    icon: string;
  };
}

export function FeaturedBusinesses() {
  const [businesses, setBusinesses] = useState<Business[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    async function fetchFeaturedBusinesses() {
      try {
        const response = await api.getBusinesses({ 
          featured: true, 
          limit: 6 
        });
        
        // Defensive programming: handle different response formats
        if (response && response.businesses && Array.isArray(response.businesses)) {
          setBusinesses(response.businesses);
        } else if (response && Array.isArray(response)) {
          // Handle case where response is directly an array
          setBusinesses(response);
        } else {
          console.warn('Unexpected featured businesses response format:', response);
          setBusinesses([]);
        }
      } catch (err) {
        setError('Failed to load featured businesses');
        console.error('Error fetching featured businesses:', err);
        setBusinesses([]); // Ensure we have an empty array
      } finally {
        setLoading(false);
      }
    }

    fetchFeaturedBusinesses();
  }, []);

  if (loading) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">Featured Businesses</h2>
            <p className="text-muted-foreground">Discover top-rated local businesses</p>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
            {Array.from({ length: 6 }).map((_, i) => (
              <Card key={i} className="animate-pulse">
                <div className="h-48 bg-muted"></div>
                <CardContent className="p-6">
                  <div className="h-4 bg-muted rounded mb-2"></div>
                  <div className="h-3 bg-muted rounded mb-4"></div>
                  <div className="h-3 bg-muted rounded w-24"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>
    );
  }

  if (error) {
    return (
      <section className="py-16 px-4">
        <div className="container mx-auto text-center">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </section>
    );
  }

  if (loading) {
    return (
      <>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Featured Businesses</h2>
        <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
          <div className="flex items-stretch p-4 gap-3">
            {Array.from({ length: 4 }).map((_, i) => (
              <div key={i} className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                <div className="w-full aspect-video bg-muted rounded-xl animate-pulse" />
                <div>
                  <div className="h-4 bg-muted rounded mb-2 animate-pulse" />
                  <div className="h-3 bg-muted rounded w-3/4 animate-pulse" />
                </div>
              </div>
            ))}
          </div>
        </div>
      </>
    );
  }

  if (error) {
    return (
      <>
        <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Featured Businesses</h2>
        <div className="px-4">
          <p className="text-muted-foreground">{error}</p>
        </div>
      </>
    );
  }

  return (
    <>
      <h2 className="text-foreground text-[22px] font-bold leading-tight tracking-[-0.015em] px-4 pb-3 pt-5">Featured Businesses</h2>
      <div className="flex overflow-y-auto [-ms-scrollbar-style:none] [scrollbar-width:none] [&::-webkit-scrollbar]:hidden">
        <div className="flex items-stretch p-4 gap-3">
          {Array.isArray(businesses) && businesses.length > 0 ? businesses.map((business) => (
            <Link key={business.id} to={`/business/${business.slug}`} className="group">
              <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
                <div
                  className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col group-hover:scale-105 transition-transform duration-200"
                  style={{
                    backgroundImage: business.hero_image_url
                      ? `url("${business.hero_image_url}")`
                      : `url("https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop")`
                  }}
                />
                <div>
                  <p className="text-foreground text-base font-medium leading-normal group-hover:text-primary transition-colors">
                    {business.name}
                  </p>
                  <p className="text-muted-foreground text-sm font-normal leading-normal line-clamp-2">
                    {business.short_description || 'A great local business serving the community.'}
                  </p>
                </div>
              </div>
            </Link>
          )) : (
            // Fallback content when no businesses are available
            <div className="flex h-full flex-1 flex-col gap-4 rounded-lg min-w-60">
              <div
                className="w-full bg-center bg-no-repeat aspect-video bg-cover rounded-xl flex flex-col"
                style={{
                  backgroundImage: `url("https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=400&h=300&fit=crop")`
                }}
              />
              <div>
                <p className="text-foreground text-base font-medium leading-normal">The Cozy Corner Cafe</p>
                <p className="text-muted-foreground text-sm font-normal leading-normal">A charming cafe with a warm atmosphere.</p>
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
}
