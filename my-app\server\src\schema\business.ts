import { pgTable, serial, varchar, text, decimal, boolean, timestamp, integer, time, pgEnum } from 'drizzle-orm/pg-core';
import { appSchema } from './users';

// Enums for business-related data
export const dayOfWeekEnum = pgEnum('day_of_week', [
  'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
]);

export const applicationStatusEnum = pgEnum('application_status', [
  'pending', 'approved', 'rejected', 'under_review'
]);

// Categories table
export const categories = appSchema.table('categories', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 100 }).notNull(),
  slug: varchar('slug', { length: 100 }).notNull().unique(),
  icon: varchar('icon', { length: 255 }),
  description: text('description'),
  is_active: boolean('is_active').default(true).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Businesses table
export const businesses = appSchema.table('businesses', {
  id: serial('id').primaryKey(),
  name: varchar('name', { length: 255 }).notNull(),
  slug: varchar('slug', { length: 255 }).notNull().unique(),
  category_id: integer('category_id').references(() => categories.id).notNull(),
  description: text('description'),
  short_description: varchar('short_description', { length: 500 }),
  address: text('address').notNull(),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 255 }),
  website: varchar('website', { length: 255 }),
  logo_url: varchar('logo_url', { length: 255 }),
  hero_image_url: varchar('hero_image_url', { length: 255 }),
  is_featured: boolean('is_featured').default(false).notNull(),
  is_active: boolean('is_active').default(true).notNull(),
  average_rating: decimal('average_rating', { precision: 3, scale: 2 }).default('0.00'),
  total_reviews: integer('total_reviews').default(0).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Business hours table
export const businessHours = appSchema.table('business_hours', {
  id: serial('id').primaryKey(),
  business_id: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }).notNull(),
  day_of_week: dayOfWeekEnum('day_of_week').notNull(),
  open_time: time('open_time'),
  close_time: time('close_time'),
  is_closed: boolean('is_closed').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Business images table
export const businessImages = appSchema.table('business_images', {
  id: serial('id').primaryKey(),
  business_id: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }).notNull(),
  image_url: varchar('image_url', { length: 255 }).notNull(),
  alt_text: varchar('alt_text', { length: 255 }),
  is_primary: boolean('is_primary').default(false).notNull(),
  display_order: integer('display_order').default(0).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
});

// Reviews table
export const reviews = appSchema.table('reviews', {
  id: serial('id').primaryKey(),
  business_id: integer('business_id').references(() => businesses.id, { onDelete: 'cascade' }).notNull(),
  rating: integer('rating').notNull(), // 1-5 stars
  comment: text('comment'),
  author_name: varchar('author_name', { length: 100 }).notNull(),
  author_email: varchar('author_email', { length: 255 }),
  is_verified: boolean('is_verified').default(false).notNull(),
  is_approved: boolean('is_approved').default(false).notNull(),
  created_at: timestamp('created_at').defaultNow().notNull(),
  updated_at: timestamp('updated_at').defaultNow().notNull(),
});

// Business applications table
export const businessApplications = appSchema.table('business_applications', {
  id: serial('id').primaryKey(),
  business_name: varchar('business_name', { length: 255 }).notNull(),
  category_id: integer('category_id').references(() => categories.id).notNull(),
  description: text('description').notNull(),
  address: text('address').notNull(),
  latitude: decimal('latitude', { precision: 10, scale: 8 }),
  longitude: decimal('longitude', { precision: 11, scale: 8 }),
  phone: varchar('phone', { length: 20 }),
  email: varchar('email', { length: 255 }).notNull(),
  website: varchar('website', { length: 255 }),
  contact_person: varchar('contact_person', { length: 100 }).notNull(),
  status: applicationStatusEnum('status').default('pending').notNull(),
  admin_notes: text('admin_notes'),
  submitted_at: timestamp('submitted_at').defaultNow().notNull(),
  reviewed_at: timestamp('reviewed_at'),
  approved_business_id: integer('approved_business_id').references(() => businesses.id),
});

// TypeScript types following existing patterns
export type Category = typeof categories.$inferSelect;
export type NewCategory = typeof categories.$inferInsert;

export type Business = typeof businesses.$inferSelect;
export type NewBusiness = typeof businesses.$inferInsert;

export type BusinessHours = typeof businessHours.$inferSelect;
export type NewBusinessHours = typeof businessHours.$inferInsert;

export type BusinessImage = typeof businessImages.$inferSelect;
export type NewBusinessImage = typeof businessImages.$inferInsert;

export type Review = typeof reviews.$inferSelect;
export type NewReview = typeof reviews.$inferInsert;

export type BusinessApplication = typeof businessApplications.$inferSelect;
export type NewBusinessApplication = typeof businessApplications.$inferInsert;

// Extended types for API responses
export type BusinessWithDetails = Business & {
  category: Category;
  hours: BusinessHours[];
  images: BusinessImage[];
  reviews: Review[];
};

export type CategoryWithBusinessCount = Category & {
  business_count: number;
};
